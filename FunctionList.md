# Functions that need to be Tools using MCP

# Level 1 Core Functions
create_task() "Tool: Create Task"
add_employee() "Tool: Add Employee"

# Level 2 Core Functions
edit_task() "Tool: Edit Task"
complete_task() "Tool: Complete Task"
delete_task() "Tool: Delete Task"
edit_employee() "Tool: Edit Employee"
delete_employee() "Tool: Delete Employee"

# Additional Functions
get_task_list() "Tool: Get Task List"
get_employee_list() "Tool: Get Employee List"
