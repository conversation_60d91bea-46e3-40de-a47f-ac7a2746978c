{% extends "base.html" %}

{% block title %}Edit Task - Task Assignment System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-edit"></i> Edit Task</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="description" class="form-label">Task Description *</label>
                        <textarea class="form-control" id="description" name="description" rows="4" required>{{ task.description }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Created Date</label>
                                <input type="text" class="form-control" value="{{ task.created_date.strftime('%d/%m/%Y') if task.created_date else task.created_at.strftime('%d/%m/%Y') }}" readonly>
                                <div class="form-text">Task creation date cannot be changed</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deadline" class="form-label">Deadline *</label>
                                <input type="datetime-local" class="form-control" id="deadline" name="deadline" 
                                       value="{{ task.deadline.strftime('%Y-%m-%dT%H:%M') }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Assign to Employees *</label>
                        {% if employees %}
                            <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                {% for employee in employees %}
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="{{ employee.id }}" 
                                           id="employee_{{ employee.id }}" name="employee_ids"
                                           {% if employee.id in assigned_employee_ids %}checked{% endif %}>
                                    <label class="form-check-label" for="employee_{{ employee.id }}">
                                        <strong>{{ employee.name }}</strong><br>
                                        <small class="text-muted">{{ employee.email }} | {{ employee.formatted_phone }}</small>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> No employees found. 
                                <a href="{{ url_for('add_employee') }}">Add an employee</a> first.
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="notification_type" class="form-label">Notification Schedule *</label>
                        <select class="form-select" id="notification_type" name="notification_type" required onchange="toggleNotificationValue()">
                            <option value="">Select notification schedule...</option>
                            <option value="daily" {% if task.notification_type == 'daily' %}selected{% endif %}>Daily reminders</option>
                            <option value="weekly" {% if task.notification_type == 'weekly' %}selected{% endif %}>Weekly reminders</option>
                            <option value="every_n_days" {% if task.notification_type == 'every_n_days' %}selected{% endif %}>Every N days</option>
                            <option value="days_before" {% if task.notification_type == 'days_before' %}selected{% endif %}>N days before deadline</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="notification_value_div" style="display: {% if task.notification_type in ['every_n_days', 'days_before'] %}block{% else %}none{% endif %};">
                        <label for="notification_value" class="form-label">Number of Days</label>
                        <input type="number" class="form-control" id="notification_value" name="notification_value" 
                               min="1" max="365" value="{{ task.notification_value or '' }}">
                        <div class="form-text" id="notification_help"></div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> <strong>Note:</strong> Changing notification settings will not affect already scheduled notifications. The original notification schedule will continue to run.
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" {% if not employees %}disabled{% endif %}>
                            <i class="fas fa-save"></i> Update Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleNotificationValue() {
    const notificationType = document.getElementById('notification_type').value;
    const notificationValueDiv = document.getElementById('notification_value_div');
    const notificationValue = document.getElementById('notification_value');
    const notificationHelp = document.getElementById('notification_help');
    
    if (notificationType === 'every_n_days') {
        notificationValueDiv.style.display = 'block';
        notificationValue.required = true;
        notificationHelp.textContent = 'Send reminder every N days after task creation';
    } else if (notificationType === 'days_before') {
        notificationValueDiv.style.display = 'block';
        notificationValue.required = true;
        notificationHelp.textContent = 'Send reminder N days before the deadline';
    } else {
        notificationValueDiv.style.display = 'none';
        notificationValue.required = false;
    }
}

// Set minimum datetime to current time
document.getElementById('deadline').min = new Date().toISOString().slice(0, 16);

// Initialize notification value display
toggleNotificationValue();
</script>
{% endblock %}
