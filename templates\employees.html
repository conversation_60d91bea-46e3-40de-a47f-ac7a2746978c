{% extends "base.html" %}

{% block title %}Employees - Task Assignment System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-users"></i> Employee Management</h1>
    <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
        <i class="fas fa-user-plus"></i> Add Employee
    </a>
</div>

{% if employees %}
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead class="table-dark">
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Created Date</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for employee in employees %}
            <tr>
                <td>
                    <div class="fw-bold">{{ employee.name }}</div>
                </td>
                <td>{{ employee.email }}</td>
                <td>{{ employee.formatted_phone }}</td>
                <td>{{ employee.created_at.strftime('%Y-%m-%d') }}</td>
                <td>
                    <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <form method="POST" action="{{ url_for('delete_employee', id=employee.id) }}" class="d-inline">
                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this employee? This will also remove them from all assigned tasks.')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="text-center py-5">
    <i class="fas fa-users fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No employees found</h3>
    <p class="text-muted">Add your first employee to start assigning tasks!</p>
    <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
        <i class="fas fa-user-plus"></i> Add Employee
    </a>
</div>
{% endif %}
{% endblock %}
