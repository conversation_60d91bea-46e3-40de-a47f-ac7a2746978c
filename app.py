from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from datetime import datetime, timedelta, timezone
import os
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import atexit
import pytz
import re
import requests
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///task_assignment.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)
migrate = Migrate(app, db)

# Initialize timezone
MALAYSIA_TZ = pytz.timezone('Asia/Kuala_Lumpur')

# Initialize scheduler variable (will be initialized when app runs directly)
scheduler = None

def init_scheduler():
    """Initialize and start the scheduler"""
    global scheduler
    if scheduler is None:
        scheduler = BackgroundScheduler(timezone=MALAYSIA_TZ)  # Set to Malaysia timezone
        scheduler.start()
        atexit.register(lambda: scheduler.shutdown())
        print(f"📅 Scheduler started with timezone: {scheduler.timezone}")

def get_current_time():
    """Get current time in Malaysia timezone"""
    return datetime.now(MALAYSIA_TZ)

def get_utc_time():
    """Get current UTC time"""
    return datetime.now(timezone.utc)

# WAHA Configuration
WAHA_CONFIG = {
    "base_url": "http://localhost:3000",
    "session": "default"
}

# User Configuration (Task Creator)
USER_CONFIG = {
    "phone": "+60179393171",  # Configure your phone number here
    "name": "DH"
}

# Status caching to prevent rapid API calls
import threading
status_cache = {
    "last_check": None,
    "last_status": None,
    "cache_duration": 5  # Cache for 5 seconds
}
status_lock = threading.Lock()

# Phone number formatting functions
def normalize_phone_number(phone_input):
    """
    Normalize Malaysian phone number input to +60 format
    Examples:
    - "123449807" -> "+60123449807"
    - "1156229909" -> "+601156229909"
    - "0177604822" -> "+60177604822"
    """
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone_input)

    # Handle different input formats
    if digits_only.startswith('60'):
        # Already has country code
        normalized = '+' + digits_only
    elif digits_only.startswith('0'):
        # Remove leading 0 and add +60
        normalized = '+60' + digits_only[1:]
    else:
        # Add +60 prefix
        normalized = '+60' + digits_only

    return normalized

def format_phone_display(phone_number):
    """
    Format phone number for display: (+60)17-899 5678 or (+60)11-5622 9909
    """
    if not phone_number.startswith('+60'):
        return phone_number

    # Remove +60 prefix for processing
    number_part = phone_number[3:]

    # Check if it's a valid format for display
    if (number_part.isdigit() and
        len(number_part) >= 2 and
        number_part[:2] in ['10', '11', '12', '13', '14', '15', '16', '17', '18', '19']):

        # Check if it's the special case for 11 (4 digits after area code)
        if number_part.startswith('11') and len(number_part) == 10:
            # Format: (+60)11-5622 9909 (area code + 4 digits + space + 4 digits)
            formatted = f"(+60){number_part[:2]}-{number_part[2:6]} {number_part[6:]}"
        elif len(number_part) == 9 and not number_part.startswith('11'):
            # Format: (+60)17-899 5678 (area code + 3 digits + space + 4 digits)
            formatted = f"(+60){number_part[:2]}-{number_part[2:5]} {number_part[5:]}"
        else:
            # Return unformatted for invalid lengths
            formatted = f"(+60){number_part}"
    else:
        # Return unformatted for invalid numbers
        formatted = f"(+60){number_part}"

    return formatted

def validate_malaysian_phone(phone_number):
    """
    Validate Malaysian phone number format
    Valid area codes: 10, 11, 12, 13, 14, 15, 16, 17, 18, 19
    """
    if not phone_number.startswith('+60'):
        return False

    number_part = phone_number[3:]

    # Check if all characters are digits
    if not number_part.isdigit():
        return False

    # Check area code
    if len(number_part) < 2:
        return False

    area_code = number_part[:2]
    valid_area_codes = ['10', '11', '12', '13', '14', '15', '16', '17', '18', '19']

    if area_code not in valid_area_codes:
        return False

    # Check total length based on area code
    if area_code == '11':
        # Special case: should have 10 digits total (11 + 8 more)
        return len(number_part) == 10
    else:
        # Normal case: should have 9 digits total (area code + 7 more)
        return len(number_part) == 9

# Database Models
class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship with tasks
    task_assignments = db.relationship('TaskAssignment', backref='employee', lazy=True, cascade='all, delete-orphan')

    @property
    def formatted_phone(self):
        """Return formatted phone number for display"""
        return format_phone_display(self.phone)

    @property
    def whatsapp_number(self):
        """Return phone number in WhatsApp format (without + and with @c.us)"""
        if self.phone.startswith('+'):
            # Remove + and add @c.us for WhatsApp format
            return self.phone[1:] + "@c.us"
        return self.phone + "@c.us"

    def __repr__(self):
        return f'<Employee {self.name}>'

class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.Text, nullable=False)
    deadline = db.Column(db.DateTime, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed = db.Column(db.Boolean, default=False)

    # Notification settings
    notification_type = db.Column(db.String(50), nullable=False)  # 'daily', 'weekly', 'days_before', 'every_n_days'
    notification_value = db.Column(db.Integer)  # For 'days_before' or 'every_n_days'

    # Relationship with employees
    task_assignments = db.relationship('TaskAssignment', backref='task', lazy=True, cascade='all, delete-orphan')

    @property
    def created_date(self):
        """Return created date (use created_at date if no separate created_date)"""
        return self.created_at.date()

    def __repr__(self):
        return f'<Task {self.description[:50]}>'

class TaskAssignment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=False)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)
    notified_at = db.Column(db.DateTime)  # Last notification sent

    def __repr__(self):
        return f'<TaskAssignment Task:{self.task_id} Employee:{self.employee_id}>'

# Routes
@app.route('/')
def index():
    """Main dashboard showing all tasks in table format"""
    tasks = Task.query.all()
    task_data = []

    for task in tasks:
        employees = [assignment.employee for assignment in task.task_assignments]
        # Handle created_date gracefully for existing tasks
        try:
            created_date = task.created_date if hasattr(task, 'created_date') and task.created_date else task.created_at.date()
        except:
            created_date = task.created_at.date()

        task_info = {
            'id': task.id,
            'description': task.description,
            'deadline': task.deadline,
            'created_date': created_date,
            'employees': employees,
            'completed': task.completed
        }
        task_data.append(task_info)

    return render_template('index.html', tasks=task_data)

@app.route('/employees')
def employees():
    """List all employees"""
    employees = Employee.query.all()
    return render_template('employees.html', employees=employees)

@app.route('/employees/add', methods=['GET', 'POST'])
def add_employee():
    """Add new employee"""
    if request.method == 'POST':
        name = request.form['name']
        email = request.form['email']
        phone_input = request.form['phone']

        # Normalize and validate phone number
        normalized_phone = normalize_phone_number(phone_input)

        if not validate_malaysian_phone(normalized_phone):
            flash('Invalid Malaysian phone number! Please enter a valid number (e.g., 0123456789 or 123456789)', 'error')
            return render_template('add_employee.html', name=name, email=email, phone=phone_input)

        # Check if email already exists
        existing_employee = Employee.query.filter_by(email=email).first()
        if existing_employee:
            flash('Email already exists!', 'error')
            return render_template('add_employee.html', name=name, email=email, phone=phone_input)

        employee = Employee(name=name, email=email, phone=normalized_phone)
        db.session.add(employee)
        db.session.commit()
        flash('Employee added successfully!', 'success')
        return redirect(url_for('employees'))

    return render_template('add_employee.html')

@app.route('/employees/edit/<int:id>', methods=['GET', 'POST'])
def edit_employee(id):
    """Edit employee"""
    employee = Employee.query.get_or_404(id)

    if request.method == 'POST':
        name = request.form['name']
        email = request.form['email']
        phone_input = request.form['phone']

        # Normalize and validate phone number
        normalized_phone = normalize_phone_number(phone_input)

        if not validate_malaysian_phone(normalized_phone):
            flash('Invalid Malaysian phone number! Please enter a valid number (e.g., 0123456789 or 123456789)', 'error')
            return render_template('edit_employee.html', employee=employee)

        # Check if email already exists (excluding current employee)
        existing_employee = Employee.query.filter(Employee.email == email, Employee.id != id).first()
        if existing_employee:
            flash('Email already exists!', 'error')
            return render_template('edit_employee.html', employee=employee)

        employee.name = name
        employee.email = email
        employee.phone = normalized_phone

        db.session.commit()
        flash('Employee updated successfully!', 'success')
        return redirect(url_for('employees'))

    return render_template('edit_employee.html', employee=employee)

@app.route('/employees/delete/<int:id>', methods=['POST'])
def delete_employee(id):
    """Delete employee"""
    employee = Employee.query.get_or_404(id)
    db.session.delete(employee)
    db.session.commit()
    flash('Employee deleted successfully!', 'success')
    return redirect(url_for('employees'))

@app.route('/tasks/create', methods=['GET', 'POST'])
def create_task():
    """Create new task"""
    if request.method == 'POST':
        description = request.form['description']
        deadline_str = request.form['deadline']
        deadline = datetime.strptime(deadline_str, '%Y-%m-%dT%H:%M')
        notification_type = request.form['notification_type']
        notification_value = request.form.get('notification_value', type=int)
        employee_ids = request.form.getlist('employee_ids')

        if not employee_ids:
            flash('Please select at least one employee!', 'error')
            employees = Employee.query.all()
            return render_template('create_task.html', employees=employees)

        # Create task
        task = Task(
            description=description,
            deadline=deadline,
            notification_type=notification_type,
            notification_value=notification_value
        )
        db.session.add(task)
        db.session.flush()  # Get the task ID

        # Create task assignments
        for employee_id in employee_ids:
            assignment = TaskAssignment(task_id=task.id, employee_id=int(employee_id))
            db.session.add(assignment)

        db.session.commit()

        # Schedule notifications
        schedule_task_notifications(task)

        flash('Task created successfully!', 'success')
        return redirect(url_for('index'))

    employees = Employee.query.all()
    return render_template('create_task.html', employees=employees)

@app.route('/tasks/complete/<int:id>', methods=['POST'])
def complete_task(id):
    """Mark task as completed"""
    task = Task.query.get_or_404(id)
    task.completed = True
    db.session.commit()
    flash('Task marked as completed!', 'success')
    return redirect(url_for('index'))

@app.route('/tasks/edit/<int:id>', methods=['GET', 'POST'])
def edit_task(id):
    """Edit task"""
    task = Task.query.get_or_404(id)

    if request.method == 'POST':
        # Update task details
        task.description = request.form['description']
        deadline_str = request.form['deadline']
        task.deadline = datetime.strptime(deadline_str, '%Y-%m-%dT%H:%M')
        task.notification_type = request.form['notification_type']
        task.notification_value = request.form.get('notification_value', type=int)

        # Get new employee assignments
        new_employee_ids = set(map(int, request.form.getlist('employee_ids')))
        current_employee_ids = set(assignment.employee_id for assignment in task.task_assignments)

        # Remove employees that are no longer assigned
        for assignment in task.task_assignments[:]:  # Use slice to avoid modification during iteration
            if assignment.employee_id not in new_employee_ids:
                db.session.delete(assignment)

        # Add new employee assignments
        for employee_id in new_employee_ids:
            if employee_id not in current_employee_ids:
                assignment = TaskAssignment(task_id=task.id, employee_id=employee_id)
                db.session.add(assignment)

        db.session.commit()

        # Note: We don't reschedule notifications for edited tasks to avoid confusion
        # The original notification schedule remains active

        flash('Task updated successfully!', 'success')
        return redirect(url_for('index'))

    employees = Employee.query.all()
    assigned_employee_ids = [assignment.employee_id for assignment in task.task_assignments]
    return render_template('edit_task.html', task=task, employees=employees, assigned_employee_ids=assigned_employee_ids)

@app.route('/tasks/delete/<int:id>', methods=['POST'])
def delete_task(id):
    """Delete task"""
    task = Task.query.get_or_404(id)
    db.session.delete(task)
    db.session.commit()
    flash('Task deleted successfully!', 'success')
    return redirect(url_for('index'))

# WhatsApp Notification Functions
def send_whatsapp_message(phone_number, message):
    """Send WhatsApp message via WAHA"""
    try:
        url = f"{WAHA_CONFIG['base_url']}/api/sendText"

        payload = {
            "session": WAHA_CONFIG["session"],
            "chatId": phone_number,
            "text": message
        }

        headers = {
            "Content-Type": "application/json"
        }

        response = requests.post(url, json=payload, headers=headers, timeout=10)

        # Debug: Print response details
        print(f"🔍 WAHA Response for {phone_number}: Status {response.status_code}")
        print(f"🔍 Response content: {response.text[:200]}...")

        # WAHA typically returns 200, 201, or 202 for successful message sending
        if response.status_code in [200, 201, 202]:
            print(f"✅ WhatsApp message sent successfully to {phone_number}")
            return True
        else:
            print(f"❌ Failed to send WhatsApp message to {phone_number}: {response.status_code} - {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Error sending WhatsApp message to {phone_number}: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error sending WhatsApp message: {str(e)}")
        return False

def create_task_message(employee, task, notification_type="assignment"):
    """Create WhatsApp message content for task notification"""

    # Emoji mapping for different notification types
    emoji_map = {
        "assignment": "📋",
        "daily_reminder": "⏰",
        "weekly_reminder": "📅",
        "periodic_reminder": "🔔",
        "deadline_reminder": "⚠️"
    }

    emoji = emoji_map.get(notification_type, "📢")

    # Handle created_date gracefully
    try:
        created_date = task.created_date if hasattr(task, 'created_date') and task.created_date else task.created_at.date()
        created_str = created_date.strftime('%d/%m/%Y')
    except:
        created_str = task.created_at.strftime('%d/%m/%Y')

    if notification_type == "assignment":
        message = f"""{emoji} *New Task Assigned*

Hello {employee.name}! 👋

You have been assigned a new task:

*Task:* {task.description}
*Created:* {created_str}
*Deadline:* {task.deadline.strftime('%d/%m/%Y')}

Please complete this task before the deadline. If you have any questions, please contact your supervisor.

Thank you! 🙏"""

    elif notification_type == "deadline_reminder":
        days_left = (task.deadline.date() - get_current_time().date()).days
        message = f"""{emoji} *Task Deadline Reminder*

Hello {employee.name}! 👋

This is a reminder about your upcoming task deadline:

*Task:* {task.description}
*Created:* {created_str}
*Deadline:* {task.deadline.strftime('%d/%m/%Y')}
*Days Left:* {days_left} day(s)

Please ensure you complete this task on time. ⏰

Thank you! 🙏"""

    else:  # daily, weekly, periodic reminders
        message = f"""{emoji} *Task Reminder*

Hello {employee.name}! 👋

This is a reminder about your pending task:

*Task:* {task.description}
*Created:* {created_str}
*Deadline:* {task.deadline.strftime('%d/%m/%Y')}

Please don't forget to complete this task before the deadline. 📝

Thank you! 🙏"""

    return message

def send_notification_to_user(task, notification_type="assignment"):
    """Send WhatsApp notification to the user/creator"""
    try:
        # Create user phone number in WhatsApp format
        user_phone = USER_CONFIG["phone"]
        if user_phone.startswith('+'):
            user_whatsapp = user_phone[1:] + "@c.us"
        else:
            user_whatsapp = user_phone + "@c.us"

        # Create message for user
        emoji_map = {
            "assignment": "📋",
            "daily_reminder": "⏰",
            "weekly_reminder": "📅",
            "periodic_reminder": "🔔",
            "deadline_reminder": "⚠️"
        }

        emoji = emoji_map.get(notification_type, "📢")

        # Handle created_date gracefully
        try:
            created_date = task.created_date if hasattr(task, 'created_date') and task.created_date else task.created_at.date()
            created_str = created_date.strftime('%d/%m/%Y')
        except:
            created_str = task.created_at.strftime('%d/%m/%Y')

        if notification_type == "assignment":
            message = f"""{emoji} *Task Assignment Confirmation*

Hello {USER_CONFIG["name"]}! 👋

Your task has been successfully assigned:

*Task:* {task.description}
*Created:* {created_str}
*Deadline:* {task.deadline.strftime('%d/%m/%Y')}
*Assigned to:* {', '.join([assignment.employee.name for assignment in task.task_assignments])}

All assigned employees have been notified.

Thank you! 🙏"""
        else:
            message = f"""{emoji} *Task Reminder Sent*

Hello {USER_CONFIG["name"]}! 👋

Reminder notifications have been sent for:

*Task:* {task.description}
*Created:* {created_str}
*Deadline:* {task.deadline.strftime('%d/%m/%Y')}
*Assigned to:* {', '.join([assignment.employee.name for assignment in task.task_assignments])}

All assigned employees have been reminded.

Thank you! 🙏"""

        # Send WhatsApp message to user
        success = send_whatsapp_message(user_whatsapp, message)

        if success:
            print(f"📱 User notification sent to {USER_CONFIG['name']} ({USER_CONFIG['phone']})")
        else:
            print(f"❌ Failed to send user notification")

        return success

    except Exception as e:
        print(f"❌ Error in send_notification_to_user: {str(e)}")
        return False

def send_notification(employee, task, notification_type="assignment"):
    """Send WhatsApp notification to employee and user"""
    try:
        # Create message content for employee
        message = create_task_message(employee, task, notification_type)

        # Send WhatsApp message to employee
        employee_success = send_whatsapp_message(employee.whatsapp_number, message)

        if employee_success:
            print(f"📱 WhatsApp notification sent to {employee.name} ({employee.formatted_phone})")
        else:
            print(f"❌ Failed to send WhatsApp notification to {employee.name}")
            # Fallback: print to console
            print(f"FALLBACK NOTIFICATION: {notification_type.upper()}")
            print(f"To: {employee.name} ({employee.formatted_phone})")
            print(f"Task: {task.description}")
            print(f"Deadline: {task.deadline}")
            print("-" * 50)

        # Send notification to user/creator (only once per task, not per employee)
        # We'll handle this in the calling function to avoid duplicate user notifications

        # Update last notification time
        assignment = TaskAssignment.query.filter_by(task_id=task.id, employee_id=employee.id).first()
        if assignment:
            assignment.notified_at = get_utc_time()
            db.session.commit()

        return employee_success

    except Exception as e:
        print(f"❌ Error in send_notification: {str(e)}")
        return False

def check_waha_status():
    """Check if WAHA server is running and session is active (with caching)"""
    with status_lock:
        # Check if we have a recent cached result
        now = get_utc_time()
        if (status_cache["last_check"] and
            status_cache["last_status"] is not None and
            (now - status_cache["last_check"]).total_seconds() < status_cache["cache_duration"]):
            return status_cache["last_status"]

    try:
        # Check server status
        url = f"{WAHA_CONFIG['base_url']}/api/sessions"
        response = requests.get(url, timeout=3)  # Reduced timeout for faster response

        if response.status_code == 200:
            try:
                sessions = response.json()
                session_name = WAHA_CONFIG["session"]

                # Check if our session exists and is ready
                for session in sessions:
                    if session.get("name") == session_name:
                        status = session.get("status", "STOPPED")
                        is_working = status == "WORKING"

                        # Cache the result
                        with status_lock:
                            status_cache["last_check"] = get_utc_time()
                            status_cache["last_status"] = is_working

                        print(f"📱 WAHA Session '{session_name}' status: {status}")
                        return is_working

                print(f"⚠️ WAHA Session '{session_name}' not found")
                result = False
            except (ValueError, TypeError) as e:
                print(f"❌ Invalid JSON response from WAHA: {str(e)}")
                result = False
        else:
            print(f"❌ WAHA server responded with status: {response.status_code}")
            result = False

    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to WAHA server at {WAHA_CONFIG['base_url']}")
        result = False
    except requests.exceptions.Timeout:
        print(f"❌ WAHA server timeout at {WAHA_CONFIG['base_url']}")
        result = False
    except requests.exceptions.RequestException as e:
        print(f"❌ WAHA request error: {str(e)}")
        result = False
    except Exception as e:
        print(f"❌ Unexpected error checking WAHA status: {str(e)}")
        result = False

    # Cache the result
    with status_lock:
        status_cache["last_check"] = get_utc_time()
        status_cache["last_status"] = result

    return result

def restart_waha_session():
    """Restart WAHA session"""
    try:
        session_name = WAHA_CONFIG["session"]

        # First, try to stop the session if it exists
        stop_url = f"{WAHA_CONFIG['base_url']}/api/sessions/{session_name}/stop"
        try:
            stop_response = requests.post(stop_url, timeout=10)
            print(f"🔄 Stopping session '{session_name}': {stop_response.status_code}")
        except:
            pass  # Session might not exist, continue

        # Wait a moment
        import time
        time.sleep(2)

        # Start the session
        start_url = f"{WAHA_CONFIG['base_url']}/api/sessions/{session_name}/start"
        start_response = requests.post(start_url, timeout=10)

        if start_response.status_code in [200, 201]:
            print(f"✅ WAHA Session '{session_name}' restart initiated")
            return True
        else:
            print(f"❌ Failed to restart session: {start_response.status_code} - {start_response.text}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Error restarting WAHA session: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error restarting session: {str(e)}")
        return False

def create_waha_session():
    """Create a new WAHA session if it doesn't exist"""
    try:
        session_name = WAHA_CONFIG["session"]

        # Create session payload
        payload = {
            "name": session_name,
            "config": {
                "proxy": None,
                "webhooks": []
            }
        }

        create_url = f"{WAHA_CONFIG['base_url']}/api/sessions"
        response = requests.post(create_url, json=payload, timeout=10)

        if response.status_code in [200, 201]:
            print(f"✅ WAHA Session '{session_name}' created successfully")
            return True
        elif response.status_code == 409:
            print(f"ℹ️ WAHA Session '{session_name}' already exists")
            return True
        else:
            print(f"❌ Failed to create session: {response.status_code} - {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Error creating WAHA session: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error creating session: {str(e)}")
        return False

def auto_restart_waha_session():
    """Automatically restart WAHA session if disconnected"""
    try:
        print("🔄 Checking WAHA session status for auto-restart...")

        # Check current status
        if check_waha_status():
            print("✅ WAHA session is working, no restart needed")
            return True

        print("⚠️ WAHA session not working, attempting restart...")

        # Try to create session first (in case it doesn't exist)
        create_waha_session()

        # Wait a moment
        import time
        time.sleep(2)

        # Try to restart
        restart_success = restart_waha_session()

        if restart_success:
            # Wait for session to initialize
            time.sleep(5)

            # Check if it's working now
            if check_waha_status():
                print("🎉 WAHA session successfully restarted and working!")
                return True
            else:
                print("⚠️ WAHA session restarted but not yet working (may need QR scan)")
                return False
        else:
            print("❌ Failed to restart WAHA session")
            return False

    except Exception as e:
        print(f"❌ Error in auto-restart: {str(e)}")
        return False

@app.route('/waha/status')
def waha_status():
    """API endpoint to check WAHA status"""
    try:
        status = check_waha_status()
        return jsonify({
            "waha_connected": status,
            "waha_url": WAHA_CONFIG["base_url"],
            "session": WAHA_CONFIG["session"],
            "status": "success"
        })
    except Exception as e:
        print(f"❌ Error in waha_status endpoint: {str(e)}")
        return jsonify({
            "waha_connected": False,
            "waha_url": WAHA_CONFIG["base_url"],
            "session": WAHA_CONFIG["session"],
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/task-status')
def api_task_status():
    """API endpoint for silent task status updates"""
    try:
        tasks = Task.query.all()
        task_data = []

        for task in tasks:
            task_data.append({
                'id': task.id,
                'completed': task.completed
            })

        return jsonify({
            "tasks": task_data,
            "status": "success"
        })
    except Exception as e:
        print(f"❌ Error in task status endpoint: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/tasks')
def api_tasks():
    """API endpoint to get full task list for frontend refresh"""
    try:
        tasks = Task.query.all()
        task_data = []

        for task in tasks:
            employees = [assignment.employee for assignment in task.task_assignments]
            # Handle created_date gracefully for existing tasks
            try:
                created_date = task.created_date if hasattr(task, 'created_date') and task.created_date else task.created_at.date()
            except:
                created_date = task.created_at.date()

            task_info = {
                'id': task.id,
                'description': task.description,
                'deadline': task.deadline.isoformat(),
                'created_date': created_date.isoformat(),
                'employees': [{'id': emp.id, 'name': emp.name, 'email': emp.email, 'phone': emp.formatted_phone} for emp in employees],
                'completed': task.completed
            }
            task_data.append(task_info)

        return jsonify({
            "tasks": task_data,
            "status": "success"
        })
    except Exception as e:
        print(f"❌ Error in tasks API endpoint: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/test/reminder/<int:task_id>')
def test_reminder(task_id):
    """Test endpoint to manually trigger a reminder notification"""
    try:
        task = Task.query.get_or_404(task_id)

        if task.completed:
            return jsonify({
                "status": "error",
                "message": "Cannot send reminder for completed task"
            }), 400

        results = []

        # Send reminder to all assigned employees
        for assignment in task.task_assignments:
            employee = assignment.employee
            success = send_notification(employee, task, "daily_reminder")
            results.append({
                "employee": employee.name,
                "phone": employee.formatted_phone,
                "success": success
            })

        # Send user notification
        user_success = send_notification_to_user(task, "daily_reminder")

        return jsonify({
            "status": "success",
            "message": f"Test reminder sent for task: {task.description}",
            "employee_results": results,
            "user_notification": user_success,
            "reminder_time": "10:00 AM (configured time)"
        })

    except Exception as e:
        print(f"❌ Error in test reminder: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/waha/restart', methods=['POST'])
def waha_restart():
    """API endpoint to manually restart WAHA session"""
    try:
        success = auto_restart_waha_session()
        return jsonify({
            "success": success,
            "message": "Session restart initiated" if success else "Failed to restart session"
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Error: {str(e)}"
        }), 500

@app.route('/waha/create', methods=['POST'])
def waha_create():
    """API endpoint to create WAHA session"""
    try:
        success = create_waha_session()
        return jsonify({
            "success": success,
            "message": "Session created successfully" if success else "Failed to create session"
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"Error: {str(e)}"
        }), 500

@app.route('/debug/tasks')
def debug_tasks():
    """Debug endpoint to check task notification types"""
    try:
        tasks = Task.query.filter_by(completed=False).all()
        task_info = []
        for task in tasks:
            task_info.append({
                "id": task.id,
                "description": task.description,
                "notification_type": task.notification_type,
                "notification_value": task.notification_value,
                "deadline": task.deadline.strftime('%Y-%m-%d %H:%M'),
                "employees": [assignment.employee.name for assignment in task.task_assignments]
            })

        return jsonify({
            "status": "success",
            "active_tasks": task_info,
            "total_active_tasks": len(task_info)
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/scheduler/status')
def scheduler_status():
    """API endpoint to check scheduler status and jobs"""
    try:
        if scheduler is None:
            return jsonify({
                "scheduler_running": False,
                "total_jobs": 0,
                "jobs": [],
                "current_time_utc": get_utc_time().isoformat(),
                "current_time_malaysia": get_current_time().isoformat(),
                "status": "success",
                "message": "Scheduler not initialized"
            })

        jobs = scheduler.get_jobs()
        job_list = []

        for job in jobs:
            job_info = {
                "id": job.id,
                "name": job.name,
                "func": str(job.func),
                "trigger": str(job.trigger),
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "args": job.args,
                "kwargs": job.kwargs
            }
            job_list.append(job_info)

        return jsonify({
            "scheduler_running": scheduler.running,
            "total_jobs": len(jobs),
            "jobs": job_list,
            "current_time_utc": get_utc_time().isoformat(),
            "current_time_malaysia": get_current_time().isoformat(),
            "status": "success"
        })
    except Exception as e:
        print(f"❌ Error in scheduler_status endpoint: {str(e)}")
        return jsonify({
            "scheduler_running": False,
            "total_jobs": 0,
            "jobs": [],
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/scheduler/trigger/<int:task_id>/<int:employee_id>')
def trigger_daily_notification(task_id, employee_id):
    """Manually trigger a daily notification for testing"""
    try:
        print(f"🧪 Manually triggering daily notification for task {task_id}, employee {employee_id}")
        send_daily_notification(task_id, employee_id)
        return jsonify({
            "status": "success",
            "message": f"Daily notification triggered for task {task_id}, employee {employee_id}"
        })
    except Exception as e:
        print(f"❌ Error triggering notification: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/scheduler/test')
def test_scheduler():
    """Test if scheduler is working by scheduling a job in 1 minute"""
    try:
        if scheduler is None:
            return jsonify({
                "status": "error",
                "error": "Scheduler not initialized"
            }), 500

        def test_job():
            print(f"🧪 TEST JOB EXECUTED at {get_current_time()}")

        # Schedule a test job to run in 1 minute (using Malaysia timezone)
        current_time = get_current_time()
        run_time = current_time + timedelta(minutes=1)
        scheduler.add_job(
            func=test_job,
            trigger='date',
            run_date=run_time,
            id='test_job',
            replace_existing=True
        )

        return jsonify({
            "status": "success",
            "message": f"Test job scheduled to run at {run_time.isoformat()}",
            "current_time_malaysia": current_time.isoformat(),
            "current_time_utc": get_utc_time().isoformat(),
            "scheduler_timezone": str(scheduler.timezone)
        })
    except Exception as e:
        print(f"❌ Error scheduling test job: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

def schedule_task_notifications(task):
    """Schedule notifications for a task based on notification settings"""
    # Send user notification once for task assignment
    send_notification_to_user(task, "assignment")

    for assignment in task.task_assignments:
        employee = assignment.employee

        # Send immediate assignment notification to employee
        send_notification(employee, task, "assignment")

        # Check if scheduler is initialized before scheduling
        if scheduler is None:
            print("⚠️ Scheduler not initialized, skipping periodic notification scheduling")
            continue

        # Schedule periodic notifications based on type
        if task.notification_type == 'daily':
            # Send daily notifications until deadline
            job_id = f"daily_{task.id}_{employee.id}"
            print(f"📅 Scheduling daily notification for task {task.id}, employee {employee.id} (job_id: {job_id})")
            print(f"📅 Schedule: 2:00 PM daily until {task.deadline}")
            scheduler.add_job(
                func=send_daily_notification,
                trigger='cron',
                hour=14,  # 2 PM daily
                args=[task.id, employee.id],
                id=job_id,
                replace_existing=True,
                end_date=task.deadline
            )
            print(f"✅ Daily notification job scheduled successfully")

        elif task.notification_type == 'weekly':
            # Send weekly notifications until deadline
            scheduler.add_job(
                func=send_weekly_notification,
                trigger='cron',
                day_of_week=1,  # Monday
                hour=14,  # 2 PM weekly
                args=[task.id, employee.id],
                id=f"weekly_{task.id}_{employee.id}",
                replace_existing=True,
                end_date=task.deadline
            )

        elif task.notification_type == 'every_n_days' and task.notification_value:
            # Send notification every N days
            scheduler.add_job(
                func=send_periodic_notification,
                trigger='interval',
                days=task.notification_value,
                args=[task.id, employee.id],
                id=f"every_{task.notification_value}days_{task.id}_{employee.id}",
                replace_existing=True,
                end_date=task.deadline
            )

        elif task.notification_type == 'days_before' and task.notification_value:
            # Send notification N days before deadline
            notification_date = task.deadline - timedelta(days=task.notification_value)
            if notification_date > get_current_time().replace(tzinfo=None):
                scheduler.add_job(
                    func=send_deadline_reminder,
                    trigger='date',
                    run_date=notification_date,
                    args=[task.id, employee.id],
                    id=f"deadline_{task.id}_{employee.id}",
                    replace_existing=True
                )

def send_daily_notification(task_id, employee_id):
    """Send daily notification"""
    print(f"🔔 Daily notification triggered for task {task_id}, employee {employee_id} at {get_current_time()}")
    with app.app_context():
        task = Task.query.get(task_id)
        employee = Employee.query.get(employee_id)
        if task and employee and not task.completed:
            print(f"📤 Sending daily reminder for task '{task.description}' to {employee.name}")
            send_notification(employee, task, "daily_reminder")
            # Send user notification only for the first employee to avoid duplicates
            if employee_id == task.task_assignments[0].employee_id:
                send_notification_to_user(task, "daily_reminder")
        else:
            print(f"⚠️ Skipping notification - Task: {task is not None}, Employee: {employee is not None}, Completed: {task.completed if task else 'N/A'}")

def send_weekly_notification(task_id, employee_id):
    """Send weekly notification"""
    with app.app_context():
        task = Task.query.get(task_id)
        employee = Employee.query.get(employee_id)
        if task and employee and not task.completed:
            send_notification(employee, task, "weekly_reminder")
            # Send user notification only for the first employee to avoid duplicates
            if employee_id == task.task_assignments[0].employee_id:
                send_notification_to_user(task, "weekly_reminder")

def send_periodic_notification(task_id, employee_id):
    """Send periodic notification"""
    with app.app_context():
        task = Task.query.get(task_id)
        employee = Employee.query.get(employee_id)
        if task and employee and not task.completed:
            send_notification(employee, task, "periodic_reminder")
            # Send user notification only for the first employee to avoid duplicates
            if employee_id == task.task_assignments[0].employee_id:
                send_notification_to_user(task, "periodic_reminder")

def send_deadline_reminder(task_id, employee_id):
    """Send deadline reminder notification"""
    with app.app_context():
        task = Task.query.get(task_id)
        employee = Employee.query.get(employee_id)
        if task and employee and not task.completed:
            send_notification(employee, task, "deadline_reminder")
            # Send user notification only for the first employee to avoid duplicates
            if employee_id == task.task_assignments[0].employee_id:
                send_notification_to_user(task, "deadline_reminder")

if __name__ == '__main__':
    # Initialize scheduler only when running app.py directly
    init_scheduler()

    # Schedule WAHA session auto-restart check every minute (only when running app.py directly)
    scheduler.add_job(
        func=auto_restart_waha_session,
        trigger='interval',
        minutes=1,
        id='waha_auto_restart',
        replace_existing=True
    )
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0',port=5004)