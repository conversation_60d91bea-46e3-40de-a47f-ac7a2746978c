#!/usr/bin/env python3
"""
MCP Server for Task Assignment System

This server provides MCP (Model Context Protocol) tools for managing tasks and employees
in the task assignment system. It acts as a bridge between LLMs and the Flask application.

Supported Tools:
- Level 1 Core: create_task, add_employee
- Level 2 Core: edit_task, complete_task, delete_task, edit_employee, delete_employee
- Additional: get_task_list, get_employee_list
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import sys
import os
from flask import Flask, jsonify
from flask_cors import CORS

# Add the current directory to Python path to import from app.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource,
        Tool,
        TextContent,
        ImageContent,
        EmbeddedResource,
        LoggingLevel
    )
except ImportError:
    print("MCP library not found. Please install with: pip install mcp")
    sys.exit(1)

# Import Flask app components
try:
    from app import app, db, Task, Employee, TaskAssignment
    from app import (
        normalize_phone_number,
        validate_malaysian_phone
    )
except ImportError as e:
    print(f"Error importing from app.py: {e}")
    print("Make sure app.py is in the same directory and all dependencies are installed")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("mcp-task-server")

# Initialize MCP Server
server = Server("task-assignment-server")

class TaskAssignmentMCPServer:
    """
    MCP Server for Task Assignment System

    Provides tools for managing tasks and employees through MCP protocol.
    """

    def __init__(self):
        self.app = app
        self.db = db
        logger.info("Task Assignment MCP Server initialized")

    def get_app_context(self):
        """Get Flask application context for database operations"""
        return self.app.app_context()

# Tool Definitions
@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """
    List all available MCP tools for the task assignment system.

    Returns:
        List[Tool]: Available tools categorized by functionality level
    """
    return [
        # Level 1 Core Functions
        Tool(
            name="create_task",
            description="Create a new task with description, deadline, notification settings, and assign to employees",
            inputSchema={
                "type": "object",
                "properties": {
                    "description": {
                        "type": "string",
                        "description": "Task description"
                    },
                    "deadline": {
                        "type": "string",
                        "description": "Task deadline in YYYY-MM-DD HH:MM format"
                    },
                    "notification_type": {
                        "type": "string",
                        "enum": ["daily", "weekly", "days_before", "every_n_days"],
                        "description": "Type of notification schedule"
                    },
                    "notification_value": {
                        "type": "integer",
                        "description": "Number of days for 'days_before' or 'every_n_days' notification types",
                        "minimum": 1
                    },
                    "employee_ids": {
                        "type": "array",
                        "items": {"type": "integer"},
                        "description": "List of employee IDs to assign the task to"
                    }
                },
                "required": ["description", "deadline", "notification_type", "employee_ids"]
            }
        ),

        Tool(
            name="add_employee",
            description="Add a new employee with name, email, and phone number",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Employee full name"
                    },
                    "email": {
                        "type": "string",
                        "format": "email",
                        "description": "Employee email address (must be unique)"
                    },
                    "phone": {
                        "type": "string",
                        "description": "Malaysian phone number (e.g., 0123456789 or 123456789)"
                    }
                },
                "required": ["name", "email", "phone"]
            }
        ),

        # Level 2 Core Functions
        Tool(
            name="edit_task",
            description="Edit an existing task's details, deadline, notification settings, or assigned employees",
            inputSchema={
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "integer",
                        "description": "ID of the task to edit"
                    },
                    "description": {
                        "type": "string",
                        "description": "Updated task description"
                    },
                    "deadline": {
                        "type": "string",
                        "description": "Updated deadline in YYYY-MM-DD HH:MM format"
                    },
                    "notification_type": {
                        "type": "string",
                        "enum": ["daily", "weekly", "days_before", "every_n_days"],
                        "description": "Updated notification schedule type"
                    },
                    "notification_value": {
                        "type": "integer",
                        "description": "Updated number of days for notification",
                        "minimum": 1
                    },
                    "employee_ids": {
                        "type": "array",
                        "items": {"type": "integer"},
                        "description": "Updated list of employee IDs assigned to the task"
                    }
                },
                "required": ["task_id"]
            }
        ),

        Tool(
            name="complete_task",
            description="Mark a task as completed",
            inputSchema={
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "integer",
                        "description": "ID of the task to mark as completed"
                    }
                },
                "required": ["task_id"]
            }
        ),

        Tool(
            name="delete_task",
            description="Delete a task permanently",
            inputSchema={
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "integer",
                        "description": "ID of the task to delete"
                    }
                },
                "required": ["task_id"]
            }
        ),

        Tool(
            name="edit_employee",
            description="Edit an existing employee's information",
            inputSchema={
                "type": "object",
                "properties": {
                    "employee_id": {
                        "type": "integer",
                        "description": "ID of the employee to edit"
                    },
                    "name": {
                        "type": "string",
                        "description": "Updated employee name"
                    },
                    "email": {
                        "type": "string",
                        "format": "email",
                        "description": "Updated employee email"
                    },
                    "phone": {
                        "type": "string",
                        "description": "Updated Malaysian phone number"
                    }
                },
                "required": ["employee_id"]
            }
        ),

        Tool(
            name="delete_employee",
            description="Delete an employee permanently (also removes from all assigned tasks)",
            inputSchema={
                "type": "object",
                "properties": {
                    "employee_id": {
                        "type": "integer",
                        "description": "ID of the employee to delete"
                    }
                },
                "required": ["employee_id"]
            }
        ),

        # Additional Functions
        Tool(
            name="get_task_list",
            description="Get a list of all tasks with their details and assigned employees",
            inputSchema={
                "type": "object",
                "properties": {
                    "include_completed": {
                        "type": "boolean",
                        "description": "Whether to include completed tasks in the list",
                        "default": True
                    },
                    "employee_id": {
                        "type": "integer",
                        "description": "Filter tasks by specific employee ID (optional)"
                    }
                }
            }
        ),

        Tool(
            name="get_employee_list",
            description="Get a list of all employees with their contact information. When user don't know employee ID, use this tool.",
            inputSchema={
                "type": "object",
                "properties": {
                    "include_task_count": {
                        "type": "boolean",
                        "description": "Whether to include the number of assigned tasks for each employee",
                        "default": False
                    }
                }
            }
        )
    ]

# HTTP Endpoint for n8n AI Agent
def create_tools_endpoint():
    """
    Create HTTP endpoint to expose available tools for n8n AI agent

    Returns:
        Flask app with /tools endpoint
    """
    from flask import Flask

    # Create a separate Flask app for the HTTP endpoint
    http_app = Flask("mcp-tools-api")

    # Enable CORS for n8n access
    try:
        CORS(http_app)
    except NameError:
        # If flask-cors is not installed, add manual CORS headers
        @http_app.after_request
        def after_request(response):
            response.headers.add('Access-Control-Allow-Origin', '*')
            response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
            response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
            return response

    @http_app.route('/tools', methods=['GET'])
    def get_tools():
        """
        HTTP endpoint to get available MCP tools

        Returns:
            JSON response with tools list and metadata
        """
        logger.info("Received request for /tools endpoint")
        try:
            # Get the tools list synchronously by running the async function
            import asyncio
            tools_list = asyncio.run(handle_list_tools())

            # Convert tools to JSON-serializable format
            tools_data = []
            for tool in tools_list:
                tool_data = {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema
                }
                tools_data.append(tool_data)

            response = {
                "status": "success",
                "server_name": "task-assignment-server",
                "server_version": "1.0.0",
                "tools_count": len(tools_data),
                "tools": tools_data,
                "categories": {
                    "level_1_core": ["create_task", "add_employee"],
                    "level_2_core": ["edit_task", "complete_task", "delete_task", "edit_employee", "delete_employee"],
                    "additional": ["get_task_list", "get_employee_list"]
                },
                "usage": {
                    "endpoint": "/call_tool",
                    "method": "POST",
                    "description": "Use the unified /call_tool endpoint to execute any tool",
                    "request_format": {
                        "tool_name": "string (required) - Name of the tool to execute",
                        "arguments": "object (required) - Tool-specific arguments as defined in inputSchema"
                    },
                    "example": {
                        "tool_name": "create_task",
                        "arguments": {
                            "description": "Complete project documentation",
                            "deadline": "2024-12-31 23:59",
                            "notification_type": "daily",
                            "employee_ids": [1, 2]
                        }
                    }
                },
                "note": "Use /call_tool for all tool executions."
            }

            return jsonify(response)

        except Exception as e:
            logger.error(f"Error in /tools endpoint: {str(e)}")
            return jsonify({
                "status": "error",
                "error": str(e),
                "server_name": "task-assignment-server"
            }), 500

    @http_app.route('/call_tool', methods=['POST'])
    def call_tool():
        """
        Unified HTTP endpoint to call any MCP tool

        Expected JSON payload:
        {
            "tool_name": "create_task",
            "arguments": {
                "description": "Task description",
                "deadline": "2024-12-31 23:59",
                ...
            }
        }

        Returns:
            JSON response with tool execution results
        """
        logger.info("Received request for /call_tool endpoint")
        try:
            from flask import request
            # Get JSON data from request
            data = request.get_json()
            if not data:
                return jsonify({
                    "status": "error",
                    "error": "No JSON data provided"
                }), 400

            # Extract tool name and arguments
            tool_name = data.get('tool_name')
            arguments = data.get('arguments', {})

            # Handle case where arguments might be a JSON string (from n8n AI Agent)
            if isinstance(arguments, str):
                try:
                    arguments = json.loads(arguments)
                except json.JSONDecodeError as e:
                    return jsonify({
                        "status": "error",
                        "error": f"Invalid JSON in arguments: {str(e)}"
                    }), 400

            if not tool_name:
                return jsonify({
                    "status": "error",
                    "error": "Missing 'tool_name' in request"
                }), 400

            logger.info(f"Executing tool: {tool_name} with arguments: {arguments}")

            # Execute the tool call using the existing MCP handler
            import asyncio
            result = asyncio.run(handle_call_tool(tool_name, arguments))

            # Convert TextContent results to JSON-serializable format
            response_data = []
            for content in result:
                if hasattr(content, 'text'):
                    response_data.append({
                        "type": "text",
                        "content": content.text
                    })
                else:
                    response_data.append({
                        "type": "unknown",
                        "content": str(content)
                    })

            return jsonify({
                "status": "success",
                "tool_name": tool_name,
                "results": response_data
            })

        except Exception as e:
            logger.error(f"Error in /call_tool endpoint: {str(e)}")
            return jsonify({
                "status": "error",
                "error": str(e),
                "tool_name": data.get('tool_name', 'unknown') if 'data' in locals() else 'unknown'
            }), 500









    # API Documentation Endpoint
    @http_app.route('/api/info', methods=['GET'])
    def api_info():
        """API information and available endpoints"""
        return jsonify({
            "server_name": "task-assignment-server",
            "server_version": "1.0.0",
            "description": "MCP Server for Task Assignment System",
            "endpoints": {
                "discovery": {
                    "GET /tools": "Get list of available tools and their schemas"
                },
                "execution": {
                    "POST /call_tool": "Execute any tool using unified endpoint"
                },
                "info": {
                    "GET /api/info": "This endpoint - API documentation"
                }
            },
            "usage": {
                "discovery": "Call GET /tools to see available tools and their input schemas",
                "execution": "Call POST /call_tool with JSON payload: {\"tool_name\": \"tool_name\", \"arguments\": {...}}",
                "content_type": "application/json",
                "response_format": "JSON with status, tool name, and results array"
            }
        })

    return http_app

# Tool Call Handlers (Structure only - implementation will be added later)
@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """
    Handle MCP tool calls.

    Args:
        name: Name of the tool to call
        arguments: Tool arguments

    Returns:
        List[TextContent]: Tool execution results
    """

    mcp_server = TaskAssignmentMCPServer()

    try:
        with mcp_server.get_app_context():
            if name == "create_task":
                return await handle_create_task(arguments)
            elif name == "add_employee":
                return await handle_add_employee(arguments)
            elif name == "edit_task":
                return await handle_edit_task(arguments)
            elif name == "complete_task":
                return await handle_complete_task(arguments)
            elif name == "delete_task":
                return await handle_delete_task(arguments)
            elif name == "edit_employee":
                return await handle_edit_employee(arguments)
            elif name == "delete_employee":
                return await handle_delete_employee(arguments)
            elif name == "get_task_list":
                return await handle_get_task_list(arguments)
            elif name == "get_employee_list":
                return await handle_get_employee_list(arguments)
            else:
                raise ValueError(f"Unknown tool: {name}")

    except Exception as e:
        logger.error(f"Error executing tool {name}: {str(e)}")
        return [TextContent(
            type="text",
            text=f"Error executing {name}: {str(e)}"
        )]

# Tool Implementation Placeholders (to be implemented later)
async def handle_create_task(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle create_task tool call"""
    try:
        # Extract arguments
        description = arguments.get('description')
        deadline_str = arguments.get('deadline')
        notification_type = arguments.get('notification_type')
        notification_value = arguments.get('notification_value')
        employee_ids = arguments.get('employee_ids', [])

        # Validate required fields
        if not description:
            return [TextContent(type="text", text="Error: Missing required field 'description'")]
        if not deadline_str:
            return [TextContent(type="text", text="Error: Missing required field 'deadline'")]
        if not notification_type:
            return [TextContent(type="text", text="Error: Missing required field 'notification_type'")]
        if not employee_ids:
            return [TextContent(type="text", text="Error: Missing required field 'employee_ids'")]

        # Parse deadline
        try:
            deadline = datetime.strptime(deadline_str, '%Y-%m-%d %H:%M')
        except ValueError:
            return [TextContent(type="text", text="Error: Invalid deadline format. Use 'YYYY-MM-DD HH:MM'")]

        # Validate employee IDs exist
        existing_employees = Employee.query.filter(Employee.id.in_(employee_ids)).all()
        if len(existing_employees) != len(employee_ids):
            existing_ids = [emp.id for emp in existing_employees]
            missing_ids = [emp_id for emp_id in employee_ids if emp_id not in existing_ids]
            return [TextContent(type="text", text=f"Error: Employee IDs not found: {missing_ids}")]

        # Create task
        task = Task(
            description=description,
            deadline=deadline,
            notification_type=notification_type,
            notification_value=notification_value
        )
        db.session.add(task)
        db.session.flush()  # Get the task ID

        # Create task assignments
        for employee_id in employee_ids:
            assignment = TaskAssignment(task_id=task.id, employee_id=employee_id)
            db.session.add(assignment)

        db.session.commit()

        # Schedule notifications for the task (CRITICAL: This was missing!)
        try:
            from app import schedule_task_notifications
            schedule_task_notifications(task)
            logger.info(f"Scheduled notifications for task {task.id}")
        except Exception as e:
            logger.error(f"Error scheduling notifications for task {task.id}: {str(e)}")

        # Get employee names for response
        employee_names = [emp.name for emp in existing_employees]

        return [TextContent(
            type="text",
            text=f"Task created successfully! ID: {task.id}, Description: '{description}', Deadline: {deadline_str}, Assigned to: {', '.join(employee_names)}"
        )]

    except Exception as e:
        logger.error(f"Error in handle_create_task: {str(e)}")
        return [TextContent(type="text", text=f"Error creating task: {str(e)}")]

async def handle_add_employee(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle add_employee tool call"""
    try:
        # Extract arguments
        name = arguments.get('name')
        email = arguments.get('email')
        phone_input = arguments.get('phone')

        # Validate required fields
        if not name:
            return [TextContent(type="text", text="Error: Missing required field 'name'")]
        if not email:
            return [TextContent(type="text", text="Error: Missing required field 'email'")]
        if not phone_input:
            return [TextContent(type="text", text="Error: Missing required field 'phone'")]

        # Normalize and validate phone number
        normalized_phone = normalize_phone_number(phone_input)

        if not validate_malaysian_phone(normalized_phone):
            return [TextContent(type="text", text="Error: Invalid Malaysian phone number! Please enter a valid number (e.g., 0123456789 or 123456789)")]

        # Check if email already exists
        existing_employee = Employee.query.filter_by(email=email).first()
        if existing_employee:
            return [TextContent(type="text", text=f"Error: Email '{email}' already exists for employee '{existing_employee.name}'")]

        # Create employee
        employee = Employee(name=name, email=email, phone=normalized_phone)
        db.session.add(employee)
        db.session.commit()

        return [TextContent(
            type="text",
            text=f"Employee added successfully! ID: {employee.id}, Name: '{name}', Email: '{email}', Phone: '{employee.formatted_phone}'"
        )]

    except Exception as e:
        logger.error(f"Error in handle_add_employee: {str(e)}")
        return [TextContent(type="text", text=f"Error adding employee: {str(e)}")]

async def handle_edit_task(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle edit_task tool call"""
    try:
        # Extract arguments
        task_id = arguments.get('task_id')
        description = arguments.get('description')
        deadline_str = arguments.get('deadline')
        notification_type = arguments.get('notification_type')
        notification_value = arguments.get('notification_value')
        employee_ids = arguments.get('employee_ids')

        # Validate required fields
        if not task_id:
            return [TextContent(type="text", text="Error: Missing required field 'task_id'")]

        # Find the task
        task = Task.query.get(task_id)
        if not task:
            return [TextContent(type="text", text=f"Error: Task with ID {task_id} not found")]

        # Update task fields if provided
        if description:
            task.description = description

        if deadline_str:
            try:
                task.deadline = datetime.strptime(deadline_str, '%Y-%m-%d %H:%M')
            except ValueError:
                return [TextContent(type="text", text="Error: Invalid deadline format. Use 'YYYY-MM-DD HH:MM'")]

        if notification_type:
            task.notification_type = notification_type

        if notification_value is not None:
            task.notification_value = notification_value

        # Update employee assignments if provided
        if employee_ids is not None:
            # Validate employee IDs exist
            existing_employees = Employee.query.filter(Employee.id.in_(employee_ids)).all()
            if len(existing_employees) != len(employee_ids):
                existing_ids = [emp.id for emp in existing_employees]
                missing_ids = [emp_id for emp_id in employee_ids if emp_id not in existing_ids]
                return [TextContent(type="text", text=f"Error: Employee IDs not found: {missing_ids}")]

            # Get current and new employee assignments
            new_employee_ids = set(employee_ids)
            current_employee_ids = set(assignment.employee_id for assignment in task.task_assignments)

            # Remove employees that are no longer assigned
            for assignment in task.task_assignments[:]:  # Use slice to avoid modification during iteration
                if assignment.employee_id not in new_employee_ids:
                    db.session.delete(assignment)

            # Add new employee assignments
            for employee_id in new_employee_ids:
                if employee_id not in current_employee_ids:
                    assignment = TaskAssignment(task_id=task.id, employee_id=employee_id)
                    db.session.add(assignment)

        db.session.commit()

        # Reschedule notifications for the updated task
        try:
            from app import schedule_task_notifications
            schedule_task_notifications(task)
            logger.info(f"Rescheduled notifications for updated task {task.id}")
        except Exception as e:
            logger.error(f"Error rescheduling notifications for task {task.id}: {str(e)}")

        # Get updated employee names for response
        updated_employees = [assignment.employee.name for assignment in task.task_assignments]

        return [TextContent(
            type="text",
            text=f"Task updated successfully! ID: {task.id}, Description: '{task.description}', Deadline: {task.deadline.strftime('%Y-%m-%d %H:%M')}, Assigned to: {', '.join(updated_employees)}"
        )]

    except Exception as e:
        logger.error(f"Error in handle_edit_task: {str(e)}")
        return [TextContent(type="text", text=f"Error editing task: {str(e)}")]

async def handle_complete_task(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle complete_task tool call"""
    try:
        # Extract arguments
        task_id = arguments.get('task_id')

        # Validate required fields
        if not task_id:
            return [TextContent(type="text", text="Error: Missing required field 'task_id'")]

        # Find the task
        task = Task.query.get(task_id)
        if not task:
            return [TextContent(type="text", text=f"Error: Task with ID {task_id} not found")]

        if task.completed:
            return [TextContent(type="text", text=f"Task '{task.description}' is already completed")]

        # Mark task as completed
        task.completed = True
        db.session.commit()

        return [TextContent(
            type="text",
            text=f"Task completed successfully! ID: {task.id}, Description: '{task.description}'"
        )]

    except Exception as e:
        logger.error(f"Error in handle_complete_task: {str(e)}")
        return [TextContent(type="text", text=f"Error completing task: {str(e)}")]

async def handle_delete_task(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle delete_task tool call"""
    try:
        # Extract arguments
        task_id = arguments.get('task_id')

        # Validate required fields
        if not task_id:
            return [TextContent(type="text", text="Error: Missing required field 'task_id'")]

        # Find the task
        task = Task.query.get(task_id)
        if not task:
            return [TextContent(type="text", text=f"Error: Task with ID {task_id} not found")]

        task_description = task.description

        # Delete the task (cascade will handle task assignments)
        db.session.delete(task)
        db.session.commit()

        return [TextContent(
            type="text",
            text=f"Task deleted successfully! ID: {task_id}, Description: '{task_description}'"
        )]

    except Exception as e:
        logger.error(f"Error in handle_delete_task: {str(e)}")
        return [TextContent(type="text", text=f"Error deleting task: {str(e)}")]

async def handle_edit_employee(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle edit_employee tool call"""
    try:
        # Extract arguments
        employee_id = arguments.get('employee_id')
        name = arguments.get('name')
        email = arguments.get('email')
        phone_input = arguments.get('phone')

        # Validate required fields
        if not employee_id:
            return [TextContent(type="text", text="Error: Missing required field 'employee_id'")]

        # Find the employee
        employee = Employee.query.get(employee_id)
        if not employee:
            return [TextContent(type="text", text=f"Error: Employee with ID {employee_id} not found")]

        # Update fields if provided
        if name:
            employee.name = name

        if email:
            # Check if email already exists for another employee
            existing_employee = Employee.query.filter(Employee.email == email, Employee.id != employee_id).first()
            if existing_employee:
                return [TextContent(type="text", text=f"Error: Email '{email}' already exists for employee '{existing_employee.name}'")]
            employee.email = email

        if phone_input:
            # Normalize and validate phone number
            normalized_phone = normalize_phone_number(phone_input)
            if not validate_malaysian_phone(normalized_phone):
                return [TextContent(type="text", text="Error: Invalid Malaysian phone number! Please enter a valid number (e.g., 0123456789 or 123456789)")]
            employee.phone = normalized_phone

        db.session.commit()

        return [TextContent(
            type="text",
            text=f"Employee updated successfully! ID: {employee.id}, Name: '{employee.name}', Email: '{employee.email}', Phone: '{employee.formatted_phone}'"
        )]

    except Exception as e:
        logger.error(f"Error in handle_edit_employee: {str(e)}")
        return [TextContent(type="text", text=f"Error editing employee: {str(e)}")]

async def handle_delete_employee(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle delete_employee tool call"""
    try:
        # Extract arguments
        employee_id = arguments.get('employee_id')

        # Validate required fields
        if not employee_id:
            return [TextContent(type="text", text="Error: Missing required field 'employee_id'")]

        # Find the employee
        employee = Employee.query.get(employee_id)
        if not employee:
            return [TextContent(type="text", text=f"Error: Employee with ID {employee_id} not found")]

        employee_name = employee.name

        # Check how many active tasks this employee has
        active_tasks_count = db.session.query(TaskAssignment).join(Task).filter(
            TaskAssignment.employee_id == employee_id,
            Task.completed == False
        ).count()

        # Delete the employee (cascade will handle task assignments)
        db.session.delete(employee)
        db.session.commit()

        message = f"Employee deleted successfully! ID: {employee_id}, Name: '{employee_name}'"
        if active_tasks_count > 0:
            message += f" (Note: Employee was removed from {active_tasks_count} active task(s))"

        return [TextContent(type="text", text=message)]

    except Exception as e:
        logger.error(f"Error in handle_delete_employee: {str(e)}")
        return [TextContent(type="text", text=f"Error deleting employee: {str(e)}")]

async def handle_get_task_list(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle get_task_list tool call"""
    try:
        include_completed = arguments.get('include_completed', True)
        employee_id = arguments.get('employee_id')

        # Build query
        query = Task.query
        if not include_completed:
            query = query.filter(Task.completed == False)

        tasks = query.order_by(Task.deadline.asc()).all()

        # Filter by employee if specified
        if employee_id:
            tasks = [task for task in tasks if any(assignment.employee_id == employee_id for assignment in task.task_assignments)]

        if not tasks:
            return [TextContent(type="text", text="No tasks found.")]

        # Format task list
        task_list = []
        for task in tasks:
            # Get assigned employees
            assigned_employees = [assignment.employee.name for assignment in task.task_assignments]
            status = "✅ Completed" if task.completed else "⏳ Pending"

            task_info = f"ID: {task.id} | {task.description} | Deadline: {task.deadline.strftime('%Y-%m-%d %H:%M')} | Status: {status} | Assigned to: {', '.join(assigned_employees)}"
            task_list.append(task_info)

        result = f"Found {len(task_list)} task(s):\n\n" + "\n".join(task_list)
        return [TextContent(type="text", text=result)]

    except Exception as e:
        logger.error(f"Error in handle_get_task_list: {str(e)}")
        return [TextContent(type="text", text=f"Error getting task list: {str(e)}")]

async def handle_get_employee_list(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle get_employee_list tool call"""
    try:
        include_task_count = arguments.get('include_task_count', False)

        employees = Employee.query.order_by(Employee.name.asc()).all()

        if not employees:
            return [TextContent(type="text", text="No employees found.")]

        # Format employee list
        employee_list = []
        for employee in employees:
            if include_task_count:
                # Count active tasks for this employee
                active_tasks = db.session.query(TaskAssignment).join(Task).filter(
                    TaskAssignment.employee_id == employee.id,
                    Task.completed == False
                ).count()
                employee_info = f"ID: {employee.id} | {employee.name} | {employee.email} | {employee.formatted_phone} | Active tasks: {active_tasks}"
            else:
                employee_info = f"ID: {employee.id} | {employee.name} | {employee.email} | {employee.formatted_phone}"

            employee_list.append(employee_info)

        result = f"Found {len(employee_list)} employee(s):\n\n" + "\n".join(employee_list)
        return [TextContent(type="text", text=result)]

    except Exception as e:
        logger.error(f"Error in handle_get_employee_list: {str(e)}")
        return [TextContent(type="text", text=f"Error getting employee list: {str(e)}")]

# HTTP Server for n8n AI Agent
def run_http_server(host="localhost", port=5105):
    """
    Run HTTP server to expose tools endpoint for n8n AI agent

    Args:
        host: Server host (default: localhost)
        port: Server port (default: 5105)
    """
    logger.info(f"Starting HTTP Tools API Server on {host}:{port}")
    logger.info(f"Tools endpoint available at: http://{host}:{port}/tools")

    http_app = create_tools_endpoint()

    # Debug: List all registered routes
    logger.info("Registered routes:")
    for rule in http_app.url_map.iter_rules():
        logger.info(f"  {rule.methods} {rule.rule}")

    http_app.run(host=host, port=port, debug=True)

# Server Entry Point
async def main():
    """Main entry point for the MCP server"""
    logger.info("Starting Task Assignment MCP Server...")

    # Initialize server options
    options = InitializationOptions(
        server_name="task-assignment-server",
        server_version="1.0.0",
        capabilities={
            "tools": {}
        }
    )

    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            options
        )

if __name__ == "__main__":
    import sys

    # Check command line arguments to determine mode
    if len(sys.argv) > 1 and sys.argv[1] == "--http":
        # Run HTTP server mode for n8n AI agent
        host = sys.argv[2] if len(sys.argv) > 2 else "localhost"
        port = int(sys.argv[3]) if len(sys.argv) > 3 else 5105
        run_http_server(host, port)
    else:
        # Run MCP server mode (default)
        asyncio.run(main())